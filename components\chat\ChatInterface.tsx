"use client"

import { useState, useCallback, useEffect } from "react"
import { Message, ChatSession } from "@/types/chat"
import { MessageList } from "./MessageList"
import { ChatInput } from "./ChatInput"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Plus, ChevronDown, Search, Check, Sparkles, Zap, Brain, Loader2, AlertCircle, MessageCircle } from "lucide-react"
import { useAuth } from "@/lib/auth-context"
import {
  createChatSession,
  updateChatSession,
  getUserChatSessions,
  generateChatTitle
} from "@/lib/firestore"

type ModelOption = {
  id: string
  name: string
  description: string
  icon: React.ComponentType<{ className?: string }>
  gradient: string
  tier: string
  family: string
}

const modelOptions: ModelOption[] = [
  // Gemini Family
  {
    id: "gemini-2.5-flash-preview-05-20",
    name: "Gemini 2.5 Flash Preview",
    description: "Latest preview with enhanced capabilities",
    icon: Zap,
    gradient: "from-blue-500 to-cyan-500",
    tier: "Preview",
    family: "Gemini"
  },
  {
    id: "gemini-2.5-pro-preview-05-06",
    name: "Gemini 2.5 Pro",
    description: "Most capable model",
    icon: Brain,
    gradient: "from-purple-500 to-indigo-500",
    tier: "Pro",
    family: "Gemini"
  },
  {
    id: "gemini-2.0-flash-001",
    name: "Gemini 2.0 Flash",
    description: "Fast and efficient model",
    icon: Sparkles,
    gradient: "from-green-500 to-emerald-500",
    tier: "Standard",
    family: "Gemini"
  },
  // ChatGPT Family
  {
    id: "gpt-4o",
    name: "GPT-4o",
    description: "Most advanced multimodal model",
    icon: Brain,
    gradient: "from-emerald-500 to-teal-500",
    tier: "Pro",
    family: "ChatGPT"
  },
  {
    id: "gpt-4o-mini",
    name: "GPT-4o Mini",
    description: "Fast and cost-effective",
    icon: Zap,
    gradient: "from-green-400 to-emerald-400",
    tier: "Standard",
    family: "ChatGPT"
  },
  // Claude Family
  {
    id: "claude-3.5-sonnet",
    name: "Claude 3.5 Sonnet",
    description: "Best for complex reasoning",
    icon: Sparkles,
    gradient: "from-orange-500 to-red-500",
    tier: "Pro",
    family: "Claude"
  },
]

export function ChatInterface() {
  const { user, loading } = useAuth()
  const [messages, setMessages] = useState<Message[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [selectedModel, setSelectedModel] = useState<ModelOption>(modelOptions[0])
  const [searchQuery, setSearchQuery] = useState("")
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null)
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([])
  const [isLoadingHistory, setIsLoadingHistory] = useState(false)
  const [saveError, setSaveError] = useState<string | null>(null)

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="flex flex-col h-screen bg-gray-50 items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin text-indigo-600 mb-4" />
        <p className="text-gray-600">Loading...</p>
      </div>
    )
  }

  // Show sign-in prompt if user is not authenticated
  if (!user) {
    return (
      <div className="flex flex-col h-screen bg-gray-50 items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <div className="w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-6">
            <MessageCircle className="w-8 h-8 text-white" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Sign in to Chat</h2>
          <p className="text-gray-600 mb-6">
            Please sign in to start chatting with AI and save your conversation history.
          </p>
          <Button
            onClick={() => window.location.href = '/SignInPage'}
            className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700"
          >
            Sign In
          </Button>
        </div>
      </div>
    )
  }

  // Load chat history when user is authenticated
  useEffect(() => {
    const loadChatHistory = async () => {
      if (!user) return

      setIsLoadingHistory(true)
      try {
        const sessions = await getUserChatSessions(user.uid)
        setChatSessions(sessions)
      } catch (error) {
        console.error('Error loading chat history:', error)
        setSaveError('Failed to load chat history')
      } finally {
        setIsLoadingHistory(false)
      }
    }

    loadChatHistory()
  }, [user])

  // Filter models based on search query
  const filteredModels = modelOptions.filter(model =>
    model.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    model.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    model.tier.toLowerCase().includes(searchQuery.toLowerCase()) ||
    model.family.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // Group filtered models by family
  const groupedModels = filteredModels.reduce((acc, model) => {
    if (!acc[model.family]) {
      acc[model.family] = []
    }
    acc[model.family].push(model)
    return acc
  }, {} as Record<string, ModelOption[]>)

  // Get family metadata
  const getFamilyInfo = (familyName: string) => {
    switch (familyName) {
      case "Gemini":
        return { icon: Brain, gradient: "from-blue-500 to-purple-500" }
      case "ChatGPT":
        return { icon: Sparkles, gradient: "from-green-500 to-emerald-500" }
      case "Claude":
        return { icon: Zap, gradient: "from-orange-500 to-red-500" }
      default:
        return { icon: Brain, gradient: "from-gray-500 to-gray-600" }
    }
  }

  // Generate a unique ID for messages
  const generateMessageId = () => {
    return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  // Save current chat session to Firestore
  const saveChatSession = useCallback(async (messages: Message[]) => {
    if (!user || messages.length === 0) return

    try {
      setSaveError(null)

      if (currentSession) {
        // Update existing session
        await updateChatSession(currentSession.id, { messages })

        // Update local state
        const updatedSession = { ...currentSession, messages, updatedAt: new Date() }
        setCurrentSession(updatedSession)
        setChatSessions(prev =>
          prev.map(session =>
            session.id === currentSession.id ? updatedSession : session
          )
        )
      } else {
        // Create new session
        const firstUserMessage = messages.find(msg => msg.role === 'user')
        const title = firstUserMessage ? generateChatTitle(firstUserMessage.content) : 'New Chat'

        const sessionData = {
          title,
          messages,
          userId: user.uid,
          model: selectedModel.id
        }

        const sessionId = await createChatSession(sessionData)

        const newSession: ChatSession = {
          id: sessionId,
          ...sessionData,
          createdAt: new Date(),
          updatedAt: new Date()
        }

        setCurrentSession(newSession)
        setChatSessions(prev => [newSession, ...prev])
      }
    } catch (error) {
      console.error('Error saving chat session:', error)
      setSaveError('Failed to save conversation')
    }
  }, [user, currentSession, selectedModel.id])

  // Start a new chat session
  const startNewChat = useCallback(() => {
    setMessages([])
    setCurrentSession(null)
    setSaveError(null)
  }, [])

  // Load a specific chat session
  const loadChatSession = useCallback((session: ChatSession) => {
    setMessages(session.messages)
    setCurrentSession(session)
    setSaveError(null)
  }, [])

  // Call Vertex AI API
  const callVertexAI = useCallback(async (userMessage: string): Promise<string> => {
    try {
      // Prepare chat history for API
      const history = messages.map(msg => ({
        role: msg.role,
        content: msg.content
      }))

      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userMessage,
          model: selectedModel.id,
          history: history,
          stream: false
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to get response from AI')
      }

      const data = await response.json()
      return data.content || 'Sorry, I could not generate a response.'
    } catch (error) {
      console.error('Vertex AI API error:', error)
      throw error
    }
  }, [selectedModel.id, messages])

  const handleSendMessage = useCallback(async (content: string) => {
    // Add user message
    const userMessage: Message = {
      id: generateMessageId(),
      content,
      role: 'user',
      timestamp: new Date(),
      status: 'sent'
    }

    const updatedMessages = [...messages, userMessage]
    setMessages(updatedMessages)
    setIsLoading(true)

    try {
      // Call Vertex AI API
      const aiResponse = await callVertexAI(content)

      // Add AI message
      const aiMessage: Message = {
        id: generateMessageId(),
        content: aiResponse,
        role: 'assistant',
        timestamp: new Date(),
        status: 'sent'
      }

      const finalMessages = [...updatedMessages, aiMessage]
      setMessages(finalMessages)

      // Save the conversation to Firestore
      await saveChatSession(finalMessages)
    } catch (error) {
      // Handle error
      const errorMessage: Message = {
        id: generateMessageId(),
        content: "I apologize, but I encountered an error while processing your request. Please try again.",
        role: 'assistant',
        timestamp: new Date(),
        status: 'error'
      }

      const finalMessages = [...updatedMessages, errorMessage]
      setMessages(finalMessages)

      // Still try to save even with error message
      await saveChatSession(finalMessages)
    } finally {
      setIsLoading(false)
    }
  }, [callVertexAI, messages, saveChatSession])

  const handleNewChat = () => {
    startNewChat()
  }

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div>
              <div className="flex items-center space-x-2">
                <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-9 px-4 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 border border-gray-200 rounded-lg shadow-sm transition-all duration-200"
                    >
                      <div className="flex items-center space-x-2">
                        <div className={`w-6 h-6 rounded-full bg-gradient-to-r ${selectedModel.gradient} flex items-center justify-center`}>
                          <selectedModel.icon className="w-3 h-3 text-white" />
                        </div>
                        <span>{selectedModel.name}</span>
                      </div>
                      <ChevronDown className="w-4 h-4 ml-2" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start" className="w-80 p-0 border-0 shadow-xl">
                    <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
                      {/* Search Header */}
                      <div className="p-4 border-b border-gray-100 bg-gray-50">
                        <div className="relative">
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                          <Input
                            placeholder="Search models..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="pl-10 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20"
                          />
                        </div>
                      </div>

                      {/* Models List */}
                      <div className="max-h-80 overflow-y-auto">
                        {Object.keys(groupedModels).length > 0 ? (
                          Object.entries(groupedModels).map(([familyName, familyModels], familyIndex) => {
                            const familyInfo = getFamilyInfo(familyName)
                            const FamilyIcon = familyInfo.icon

                            return (
                              <div key={familyName}>
                                {familyIndex > 0 && <DropdownMenuSeparator className="my-2" />}

                                {/* Family Header */}
                                <DropdownMenuLabel className="px-4 py-2 flex items-center space-x-2 text-xs font-semibold text-gray-700 bg-gray-50/50">
                                  <div className={`w-4 h-4 rounded bg-gradient-to-r ${familyInfo.gradient} flex items-center justify-center`}>
                                    <FamilyIcon className="w-2.5 h-2.5 text-white" />
                                  </div>
                                  <span>{familyName}</span>
                                </DropdownMenuLabel>

                                {/* Family Models */}
                                {familyModels.map((model) => (
                                  <DropdownMenuItem
                                    key={model.id}
                                    onClick={() => {
                                      setSelectedModel(model)
                                      setSearchQuery("")
                                      setIsDropdownOpen(false)
                                    }}
                                    className="p-4 hover:bg-gray-50 cursor-pointer border-0 focus:bg-gray-50 transition-colors duration-150 ml-2"
                                  >
                                    <div className="flex items-start space-x-3 w-full">
                                      <div className={`w-8 h-8 rounded-lg bg-gradient-to-r ${model.gradient} flex items-center justify-center flex-shrink-0`}>
                                        <model.icon className="w-4 h-4 text-white" />
                                      </div>
                                      <div className="flex-1 min-w-0">
                                        <div className="flex items-center justify-between">
                                          <div className="font-semibold text-gray-900 text-sm">{model.name}</div>
                                          {selectedModel.id === model.id && (
                                            <Check className="w-4 h-4 text-blue-600" />
                                          )}
                                        </div>
                                        <div className="text-xs text-gray-500 mt-1">{model.description}</div>
                                        <div className="flex items-center mt-2">
                                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            {model.tier}
                                          </span>
                                        </div>
                                      </div>
                                    </div>
                                  </DropdownMenuItem>
                                ))}
                              </div>
                            )
                          })
                        ) : (
                          <div className="p-4 text-center text-gray-500 text-sm">
                            No models found matching "{searchQuery}"
                          </div>
                        )}
                      </div>
                    </div>
                  </DropdownMenuContent>
                </DropdownMenu>
                <span className="text-xs text-gray-500">Ready to help</span>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleNewChat}
              className="text-gray-600 hover:text-gray-900"
            >
              <Plus className="w-4 h-4 mr-2" />
              New Chat
            </Button>

            {/* Save Status Indicator */}
            {saveError && (
              <div className="text-red-500 text-xs flex items-center">
                <AlertCircle className="w-3 h-3 mr-1" />
                {saveError}
              </div>
            )}

            {isLoadingHistory && (
              <div className="text-gray-500 text-xs flex items-center">
                <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                Loading history...
              </div>
            )}

            {currentSession && !saveError && (
              <div className="text-green-600 text-xs flex items-center">
                <Check className="w-3 h-3 mr-1" />
                Saved
              </div>
            )}
          </div>
        </div>
      </header>

      {/* Messages Area */}
      <MessageList
        messages={messages}
        isLoading={isLoading}
        onSendMessage={handleSendMessage}
      />

      {/* Input Area */}
      <ChatInput
        onSendMessage={handleSendMessage}
        disabled={isLoading}
        placeholder="Ask me anything..."
      />
    </div>
  )
}
