export interface Message {
  id: string
  content: string
  role: 'user' | 'assistant'
  timestamp: Date
  status?: 'sending' | 'sent' | 'error'
}

export interface ChatSession {
  id: string
  title: string
  messages: Message[]
  createdAt: Date
  updatedAt: Date
  userId: string // Associate session with user
  model?: string // Track which AI model was used
}

export interface ChatState {
  currentSession: ChatSession | null
  sessions: ChatSession[]
  isLoading: boolean
  error: string | null
}

// Firestore-compatible interfaces (with Timestamp instead of Date)
export interface FirestoreMessage {
  id: string
  content: string
  role: 'user' | 'assistant'
  timestamp: any // Firestore Timestamp
  status?: 'sending' | 'sent' | 'error'
}

export interface FirestoreChatSession {
  id?: string
  title: string
  messages: FirestoreMessage[]
  createdAt: any // Firestore Timestamp
  updatedAt: any // Firestore Timestamp
  userId: string
  model?: string
}
